/**
 * File Download Service
 * Service ch<PERSON>h quản lý download process từ Google Drive
 */

import fs from 'fs';
import path from 'path';
import { SupabaseClient } from '../database/supabase.js';
import { googleDriveAPI } from '../api/google-drive-api.js';
import { DownloadWorker } from './download-worker.js';
import EventEmitter from 'events';

export class FileDownloadService extends EventEmitter {
    constructor() {
        super();
        this.supabase = new SupabaseClient();
        this.workers = new Map(); // sessionId -> DownloadWorker
        this.activeSessions = new Map(); // sessionId -> session data
    }

    /**
     * Tạo download session mới
     */
    async createDownloadSession(config) {
        try {
            const {
                name,
                selectedUsers,
                downloadPath,
                concurrentDownloads = 3,
                maxRetries = 3,
                stopOnError = true,
                continueOnError = false,
                skipMimeTypes = [],
                processingOrder = 'created_at'
            } = config;

            // Validate input
            if (!name || !selectedUsers || !downloadPath) {
                throw new Error('Missing required fields: name, selectedUsers, downloadPath');
            }

            if (!Array.isArray(selectedUsers) || selectedUsers.length === 0) {
                throw new Error('selectedUsers must be a non-empty array');
            }

            // Tạo session trong database
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .insert({
                    name,
                    selected_users: selectedUsers,
                    download_path: downloadPath,
                    concurrent_downloads: concurrentDownloads,
                    max_retries: maxRetries,
                    stop_on_error: stopOnError,
                    continue_on_error: continueOnError,
                    skip_mime_types: skipMimeTypes,
                    processing_order: processingOrder,
                    status: 'pending'
                })
                .select()
                .single();

            if (error) {
                throw new Error(`Failed to create download session: ${error.message}`);
            }

            // Tính toán tổng số files cần download
            await this.calculateSessionStats(session.id, selectedUsers);

            // Lấy lại session sau khi update stats
            const { data: updatedSession, error: updateError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('*')
                .eq('id', session.id)
                .single();

            if (updateError) {
                throw new Error(`Failed to get updated session: ${updateError.message}`);
            }

            console.log(`✅ Created download session: ${session.id} - ${name}`);
            return updatedSession;

        } catch (error) {
            console.error('❌ Error creating download session:', error.message);
            throw error;
        }
    }

    /**
     * Tính toán thống kê session (tổng files, size) - OPTIMIZED VERSION
     * Sử dụng SQL aggregation thay vì fetch toàn bộ data để tránh timeout
     */
    async calculateSessionStats(sessionId, selectedUsers) {
        try {
            // Get session config to check skip_mime_types and processing_order
            const { data: sessionConfig, error: sessionError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('skip_mime_types, processing_order')
                .eq('id', sessionId)
                .single();

            if (sessionError) {
                throw new Error(`Failed to get session config: ${sessionError.message}`);
            }

            const skipMimeTypes = sessionConfig.skip_mime_types || [];

            console.log(`📊 Calculating session stats for ${selectedUsers.length} users...`);

            // Get timeout configuration
            const timeouts = this.supabase.getTimeouts();
            console.log(`⏱️ Using session stats timeout: ${timeouts.sessionStats}ms`);

            // OPTIMIZED: Sử dụng database function để tính toán nhanh hơn
            // Tính toán trực tiếp trên database để tránh timeout và memory issues
            const startTime = Date.now();

            try {
                // Try using the optimized database function first
                const { data: statsData, error: statsError } = await Promise.race([
                    this.supabase.getServiceClient().rpc('get_session_stats', {
                        selected_users: selectedUsers,
                        skip_mime_types: skipMimeTypes || []
                    }),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('Session stats calculation timeout')), timeouts.sessionStats)
                    )
                ]);

                const executionTime = Date.now() - startTime;

                if (statsError) {
                    throw new Error(`Failed to calculate stats using function: ${statsError.message}`);
                }

                if (statsData && statsData.length > 0) {
                    const stats = statsData[0];
                    const totalFiles = parseInt(stats.total_files) || 0;
                    const totalSize = parseInt(stats.total_size) || 0;

                    console.log(`📊 Stats calculated using DB function in ${executionTime}ms: ${totalFiles} files, ${this.formatFileSize(totalSize)}`);

                    // Update session stats
                    await this.supabase.getServiceClient()
                        .from('download_sessions')
                        .update({
                            total_files: totalFiles,
                            total_size: totalSize
                        })
                        .eq('id', sessionId);

                    console.log(`✅ Session stats updated successfully`);
                    return;
                }
            } catch (functionError) {
                console.log(`⚠️ Database function failed: ${functionError.message}, falling back to direct query...`);
            }

            // Fallback to direct aggregation query if function fails
            let query = this.supabase.getServiceClient()
                .from('scanned_files_cuong')
                .select('count(*), sum(size)')
                .in('user_email', selectedUsers);

            // Apply MIME type filter if specified
            if (skipMimeTypes && skipMimeTypes.length > 0) {
                query = query.not('mime_type', 'in', `(${skipMimeTypes.map(type => `"${type}"`).join(',')})`);
            }

            // Execute query with timeout handling
            const { data: statsData, error: statsError } = await Promise.race([
                query.single(),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Session stats calculation timeout')), timeouts.sessionStats)
                )
            ]);
            const executionTime = Date.now() - startTime;

            if (statsError) {
                throw new Error(`Failed to calculate stats: ${statsError.message}`);
            }

            const totalFiles = parseInt(statsData.count) || 0;
            const totalSize = parseInt(statsData.sum) || 0;

            console.log(`📊 Stats calculated in ${executionTime}ms: ${totalFiles} files, ${this.formatFileSize(totalSize)}`);

            // Update session stats
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    total_files: totalFiles,
                    total_size: totalSize
                })
                .eq('id', sessionId);

            console.log(`✅ Session stats updated successfully`);

        } catch (error) {
            console.error('❌ Error calculating session stats:', error.message);

            // Fallback to old method if aggregation fails
            console.log('🔄 Falling back to batch processing method...');
            await this.calculateSessionStatsFallback(sessionId, selectedUsers);
        }
    }

    /**
     * Fallback method: Tính toán thống kê bằng batch processing (method cũ)
     * Chỉ sử dụng khi aggregation method fails
     */
    async calculateSessionStatsFallback(sessionId, selectedUsers) {
        try {
            // Get session config
            const { data: sessionConfig, error: sessionError } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('skip_mime_types')
                .eq('id', sessionId)
                .single();

            if (sessionError) {
                throw new Error(`Failed to get session config: ${sessionError.message}`);
            }

            const skipMimeTypes = sessionConfig.skip_mime_types || [];

            // Query files với timeout tăng lên và batch size nhỏ hơn
            let totalFiles = 0;
            let totalSize = 0;
            let hasMore = true;
            let offset = 0;
            const batchSize = 500; // Giảm batch size để tránh timeout

            console.log(`📄 Using fallback batch processing with batch size: ${batchSize}`);

            while (hasMore) {
                let query = this.supabase.getServiceClient()
                    .from('scanned_files_cuong')
                    .select('size, mime_type')
                    .in('user_email', selectedUsers)
                    .range(offset, offset + batchSize - 1);

                // Apply MIME type filter if specified
                if (skipMimeTypes && skipMimeTypes.length > 0) {
                    query = query.not('mime_type', 'in', `(${skipMimeTypes.map(type => `"${type}"`).join(',')})`);
                }

                const { data: batchFiles, error } = await query;

                if (error) {
                    throw new Error(`Failed to query files: ${error.message}`);
                }

                if (batchFiles && batchFiles.length > 0) {
                    // Tính toán stats cho batch này
                    const batchCount = batchFiles.length;
                    const batchSizeBytes = batchFiles.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);

                    totalFiles += batchCount;
                    totalSize += batchSizeBytes;

                    // Check if we got a full batch
                    hasMore = batchFiles.length === batchSize;
                    offset += batchSize;

                    console.log(`📄 Processed batch: ${batchCount} files, ${this.formatFileSize(batchSizeBytes)} (Total: ${totalFiles} files, ${this.formatFileSize(totalSize)})`);
                } else {
                    hasMore = false;
                }
            }

            // Update session stats
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    total_files: totalFiles,
                    total_size: totalSize
                })
                .eq('id', sessionId);

            console.log(`✅ Fallback stats calculation completed: ${totalFiles} files, ${this.formatFileSize(totalSize)}`);

        } catch (error) {
            console.error('❌ Error in fallback stats calculation:', error.message);
            throw error;
        }
    }

    /**
     * Bắt đầu download session
     */
    async startDownloadSession(sessionId) {
        try {
            // Kiểm tra session tồn tại
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_sessions')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error || !session) {
                throw new Error(`Session not found: ${sessionId}`);
            }

            if (session.status !== 'pending' && session.status !== 'paused') {
                throw new Error(`Cannot start session with status: ${session.status}`);
            }

            // Update session status
            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    status: 'running',
                    started_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            // Tạo và khởi động worker
            const worker = new DownloadWorker(sessionId, session, this.supabase);
            this.workers.set(sessionId, worker);
            this.activeSessions.set(sessionId, session);

            // Listen to worker events
            worker.on('progress', (data) => {
                this.emit('progress', { sessionId, ...data });
            });

            worker.on('completed', (data) => {
                this.workers.delete(sessionId);
                this.activeSessions.delete(sessionId);
                this.emit('completed', { sessionId, ...data });
            });

            worker.on('error', (error) => {
                this.emit('error', { sessionId, error });
            });

            // Bắt đầu download
            worker.start();

            console.log(`🚀 Started download session: ${sessionId}`);
            return { success: true, sessionId };

        } catch (error) {
            console.error('❌ Error starting download session:', error.message);
            throw error;
        }
    }

    /**
     * Pause download session
     */
    async pauseDownloadSession(sessionId) {
        try {
            const worker = this.workers.get(sessionId);
            if (worker) {
                await worker.pause();
            }

            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({ status: 'paused' })
                .eq('id', sessionId);

            console.log(`⏸️ Paused download session: ${sessionId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Error pausing download session:', error.message);
            throw error;
        }
    }

    /**
     * Cancel download session
     */
    async cancelDownloadSession(sessionId) {
        try {
            const worker = this.workers.get(sessionId);
            if (worker) {
                await worker.cancel();
                this.workers.delete(sessionId);
                this.activeSessions.delete(sessionId);
            }

            await this.supabase.getServiceClient()
                .from('download_sessions')
                .update({
                    status: 'cancelled',
                    completed_at: new Date().toISOString()
                })
                .eq('id', sessionId);

            console.log(`❌ Cancelled download session: ${sessionId}`);
            return { success: true };

        } catch (error) {
            console.error('❌ Error cancelling download session:', error.message);
            throw error;
        }
    }

    /**
     * Lấy thông tin session
     */
    async getSessionInfo(sessionId) {
        try {
            const { data: session, error } = await this.supabase.getServiceClient()
                .from('download_session_stats')
                .select('*')
                .eq('id', sessionId)
                .single();

            if (error) {
                throw new Error(`Failed to get session info: ${error.message}`);
            }

            return session;

        } catch (error) {
            console.error('❌ Error getting session info:', error.message);
            throw error;
        }
    }

    /**
     * Lấy danh sách sessions
     */
    async listSessions(limit = 50) {
        try {
            const { data: sessions, error } = await this.supabase.getServiceClient()
                .from('download_session_stats')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to list sessions: ${error.message}`);
            }

            return sessions;

        } catch (error) {
            console.error('❌ Error listing sessions:', error.message);
            throw error;
        }
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        for (const [sessionId, worker] of this.workers) {
            try {
                await worker.cancel();
            } catch (error) {
                console.error(`Error cleaning up worker ${sessionId}:`, error.message);
            }
        }
        this.workers.clear();
        this.activeSessions.clear();
    }
}
