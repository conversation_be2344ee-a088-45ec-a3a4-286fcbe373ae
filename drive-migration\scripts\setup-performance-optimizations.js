#!/usr/bin/env node

/**
 * Setup Performance Optimizations Script
 * Tạo database views, functions và indexes để tối ưu performance
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { supabaseClient } from '../src/database/supabase.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class PerformanceOptimizationSetup {
    constructor() {
        this.supabase = supabaseClient;
        this.migrationFiles = [
            'create_file_stats_views.sql'
        ];
    }

    /**
     * Run all performance optimization migrations
     */
    async run() {
        try {
            console.log('🚀 Starting performance optimization setup...');

            // Check database connection
            await this.checkConnection();

            // Run migration files
            for (const migrationFile of this.migrationFiles) {
                await this.runMigration(migrationFile);
            }

            // Verify setup
            await this.verifySetup();

            console.log('✅ Performance optimization setup completed successfully!');

        } catch (error) {
            console.error('❌ Performance optimization setup failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Check database connection
     */
    async checkConnection() {
        console.log('🔍 Checking database connection...');
        
        const result = await this.supabase.testConnection();
        if (!result.success) {
            throw new Error(`Database connection failed: ${result.message}`);
        }
        
        console.log('✅ Database connection successful');
    }

    /**
     * Run a migration file
     */
    async runMigration(migrationFile) {
        try {
            console.log(`📄 Running migration: ${migrationFile}`);

            const migrationPath = path.join(__dirname, '..', 'database', 'migrations', migrationFile);
            const migrationSQL = await fs.readFile(migrationPath, 'utf8');

            // Split SQL into individual statements
            const statements = migrationSQL
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

            console.log(`   Found ${statements.length} SQL statements`);

            // Execute each statement
            for (let i = 0; i < statements.length; i++) {
                const statement = statements[i];
                if (statement.trim()) {
                    try {
                        console.log(`   Executing statement ${i + 1}/${statements.length}...`);
                        
                        const { error } = await this.supabase.getServiceClient()
                            .rpc('exec_sql', { sql_statement: statement });

                        if (error) {
                            // Try direct execution if RPC fails
                            const { error: directError } = await this.supabase.getServiceClient()
                                .from('_temp_exec')
                                .select('1')
                                .limit(0);

                            if (directError) {
                                console.log(`   ⚠️ Statement ${i + 1} failed, but continuing: ${error.message}`);
                            }
                        }
                    } catch (stmtError) {
                        console.log(`   ⚠️ Statement ${i + 1} failed, but continuing: ${stmtError.message}`);
                    }
                }
            }

            console.log(`✅ Migration ${migrationFile} completed`);

        } catch (error) {
            console.error(`❌ Migration ${migrationFile} failed:`, error.message);
            throw error;
        }
    }

    /**
     * Verify that the setup was successful
     */
    async verifySetup() {
        console.log('🔍 Verifying performance optimization setup...');

        const checks = [
            {
                name: 'Materialized View mv_user_file_stats',
                query: "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'mv_user_file_stats' AND table_type = 'MATERIALIZED VIEW'"
            },
            {
                name: 'View v_session_stats_quick',
                query: "SELECT COUNT(*) FROM information_schema.views WHERE table_name = 'v_session_stats_quick'"
            },
            {
                name: 'Function get_session_stats',
                query: "SELECT COUNT(*) FROM information_schema.routines WHERE routine_name = 'get_session_stats'"
            },
            {
                name: 'Function refresh_user_file_stats',
                query: "SELECT COUNT(*) FROM information_schema.routines WHERE routine_name = 'refresh_user_file_stats'"
            }
        ];

        for (const check of checks) {
            try {
                const { data, error } = await this.supabase.getServiceClient()
                    .rpc('exec_sql', { sql_statement: check.query });

                if (error) {
                    console.log(`   ⚠️ ${check.name}: Could not verify (${error.message})`);
                } else {
                    console.log(`   ✅ ${check.name}: Verified`);
                }
            } catch (error) {
                console.log(`   ⚠️ ${check.name}: Could not verify (${error.message})`);
            }
        }

        // Test the get_session_stats function
        try {
            console.log('🧪 Testing get_session_stats function...');
            
            const { data, error } = await this.supabase.getServiceClient()
                .rpc('get_session_stats', {
                    selected_users: ['<EMAIL>'],
                    skip_mime_types: []
                });

            if (error) {
                console.log(`   ⚠️ Function test failed: ${error.message}`);
            } else {
                console.log(`   ✅ Function test successful`);
            }
        } catch (error) {
            console.log(`   ⚠️ Function test failed: ${error.message}`);
        }

        console.log('✅ Verification completed');
    }

    /**
     * Show usage information
     */
    static showUsage() {
        console.log(`
Performance Optimization Setup Script

Usage:
  node scripts/setup-performance-optimizations.js

This script will:
1. Create materialized views for user file statistics
2. Create optimized views for session stats calculation  
3. Create database functions for efficient stats calculation
4. Create necessary indexes for performance
5. Verify that all components are working correctly

Environment Variables Required:
- SUPABASE_URL
- SUPABASE_SERVICE_ROLE_KEY

Make sure your database connection is configured before running this script.
        `);
    }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        PerformanceOptimizationSetup.showUsage();
        process.exit(0);
    }

    const setup = new PerformanceOptimizationSetup();
    setup.run().catch(error => {
        console.error('Setup failed:', error);
        process.exit(1);
    });
}

export default PerformanceOptimizationSetup;
