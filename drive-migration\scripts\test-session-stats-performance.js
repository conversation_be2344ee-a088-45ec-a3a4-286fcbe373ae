#!/usr/bin/env node

/**
 * Test Session Stats Performance Script
 * So sánh performance giữa method cũ và method mới
 */

import { supabaseClient } from '../src/database/supabase.js';
import { FileDownloadService } from '../src/services/file-download-service.js';

class SessionStatsPerformanceTester {
    constructor() {
        this.supabase = supabaseClient;
        this.downloadService = new FileDownloadService();
        this.testResults = [];
    }

    /**
     * Run performance tests
     */
    async run() {
        try {
            console.log('🧪 Starting session stats performance tests...');

            // Get test users
            const testUsers = await this.getTestUsers();
            if (testUsers.length === 0) {
                console.log('⚠️ No test users found in scanned_files_cuong table');
                return;
            }

            console.log(`📊 Testing with ${testUsers.length} users`);

            // Test different user set sizes
            const testSizes = [1, 5, 10, Math.min(20, testUsers.length)];

            for (const size of testSizes) {
                if (size <= testUsers.length) {
                    const selectedUsers = testUsers.slice(0, size);
                    await this.testSessionStatsPerformance(selectedUsers, size);
                }
            }

            // Show results summary
            this.showResultsSummary();

        } catch (error) {
            console.error('❌ Performance test failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Get test users from database
     */
    async getTestUsers() {
        try {
            const { data, error } = await this.supabase.getServiceClient()
                .from('scanned_files_cuong')
                .select('user_email')
                .limit(50);

            if (error) {
                throw new Error(`Failed to get test users: ${error.message}`);
            }

            // Get unique users
            const uniqueUsers = [...new Set(data.map(row => row.user_email))];
            return uniqueUsers;

        } catch (error) {
            console.error('Failed to get test users:', error.message);
            return [];
        }
    }

    /**
     * Test session stats performance for a given set of users
     */
    async testSessionStatsPerformance(selectedUsers, testSize) {
        console.log(`\n🔬 Testing with ${testSize} users...`);

        const testResult = {
            userCount: testSize,
            users: selectedUsers,
            tests: {}
        };

        // Test 1: Database function method (new optimized method)
        try {
            console.log('   📈 Testing database function method...');
            const startTime = Date.now();
            
            const { data: functionData, error: functionError } = await this.supabase.getServiceClient()
                .rpc('get_session_stats', {
                    selected_users: selectedUsers,
                    skip_mime_types: []
                });

            const functionTime = Date.now() - startTime;

            if (functionError) {
                throw new Error(functionError.message);
            }

            const functionStats = functionData[0] || {};
            testResult.tests.databaseFunction = {
                executionTime: functionTime,
                totalFiles: parseInt(functionStats.total_files) || 0,
                totalSize: parseInt(functionStats.total_size) || 0,
                success: true
            };

            console.log(`      ✅ Function method: ${functionTime}ms, ${testResult.tests.databaseFunction.totalFiles} files`);

        } catch (error) {
            testResult.tests.databaseFunction = {
                executionTime: null,
                error: error.message,
                success: false
            };
            console.log(`      ❌ Function method failed: ${error.message}`);
        }

        // Test 2: Direct aggregation method
        try {
            console.log('   📊 Testing direct aggregation method...');
            const startTime = Date.now();
            
            const { data: aggregationData, error: aggregationError } = await this.supabase.getServiceClient()
                .from('scanned_files_cuong')
                .select('count(*), sum(size)')
                .in('user_email', selectedUsers)
                .single();

            const aggregationTime = Date.now() - startTime;

            if (aggregationError) {
                throw new Error(aggregationError.message);
            }

            testResult.tests.directAggregation = {
                executionTime: aggregationTime,
                totalFiles: parseInt(aggregationData.count) || 0,
                totalSize: parseInt(aggregationData.sum) || 0,
                success: true
            };

            console.log(`      ✅ Aggregation method: ${aggregationTime}ms, ${testResult.tests.directAggregation.totalFiles} files`);

        } catch (error) {
            testResult.tests.directAggregation = {
                executionTime: null,
                error: error.message,
                success: false
            };
            console.log(`      ❌ Aggregation method failed: ${error.message}`);
        }

        // Test 3: Fallback batch method (old method)
        try {
            console.log('   📄 Testing fallback batch method...');
            const startTime = Date.now();
            
            // Simulate the old batch processing method
            let totalFiles = 0;
            let totalSize = 0;
            let hasMore = true;
            let offset = 0;
            const batchSize = 500;

            while (hasMore) {
                const { data: batchFiles, error } = await this.supabase.getServiceClient()
                    .from('scanned_files_cuong')
                    .select('size')
                    .in('user_email', selectedUsers)
                    .range(offset, offset + batchSize - 1);

                if (error) {
                    throw new Error(error.message);
                }

                if (batchFiles && batchFiles.length > 0) {
                    totalFiles += batchFiles.length;
                    totalSize += batchFiles.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);
                    
                    hasMore = batchFiles.length === batchSize;
                    offset += batchSize;
                } else {
                    hasMore = false;
                }
            }

            const batchTime = Date.now() - startTime;

            testResult.tests.batchProcessing = {
                executionTime: batchTime,
                totalFiles: totalFiles,
                totalSize: totalSize,
                success: true
            };

            console.log(`      ✅ Batch method: ${batchTime}ms, ${totalFiles} files`);

        } catch (error) {
            testResult.tests.batchProcessing = {
                executionTime: null,
                error: error.message,
                success: false
            };
            console.log(`      ❌ Batch method failed: ${error.message}`);
        }

        // Calculate performance improvements
        this.calculatePerformanceGains(testResult);
        this.testResults.push(testResult);
    }

    /**
     * Calculate performance improvements
     */
    calculatePerformanceGains(testResult) {
        const { databaseFunction, directAggregation, batchProcessing } = testResult.tests;

        if (databaseFunction.success && batchProcessing.success) {
            const improvement = ((batchProcessing.executionTime - databaseFunction.executionTime) / batchProcessing.executionTime * 100).toFixed(1);
            console.log(`      🚀 Function vs Batch: ${improvement}% faster`);
        }

        if (directAggregation.success && batchProcessing.success) {
            const improvement = ((batchProcessing.executionTime - directAggregation.executionTime) / batchProcessing.executionTime * 100).toFixed(1);
            console.log(`      🚀 Aggregation vs Batch: ${improvement}% faster`);
        }

        if (databaseFunction.success && directAggregation.success) {
            const comparison = databaseFunction.executionTime < directAggregation.executionTime ? 'faster' : 'slower';
            const diff = Math.abs(databaseFunction.executionTime - directAggregation.executionTime);
            console.log(`      📊 Function vs Aggregation: ${diff}ms ${comparison}`);
        }
    }

    /**
     * Show results summary
     */
    showResultsSummary() {
        console.log('\n📋 Performance Test Results Summary');
        console.log('=====================================');

        const headers = ['Users', 'Function (ms)', 'Aggregation (ms)', 'Batch (ms)', 'Best Method'];
        console.log(headers.join('\t'));
        console.log('-'.repeat(80));

        for (const result of this.testResults) {
            const func = result.tests.databaseFunction;
            const agg = result.tests.directAggregation;
            const batch = result.tests.batchProcessing;

            const funcTime = func.success ? func.executionTime : 'FAIL';
            const aggTime = agg.success ? agg.executionTime : 'FAIL';
            const batchTime = batch.success ? batch.executionTime : 'FAIL';

            // Determine best method
            let bestMethod = 'N/A';
            let bestTime = Infinity;

            if (func.success && func.executionTime < bestTime) {
                bestTime = func.executionTime;
                bestMethod = 'Function';
            }
            if (agg.success && agg.executionTime < bestTime) {
                bestTime = agg.executionTime;
                bestMethod = 'Aggregation';
            }
            if (batch.success && batch.executionTime < bestTime) {
                bestTime = batch.executionTime;
                bestMethod = 'Batch';
            }

            const row = [result.userCount, funcTime, aggTime, batchTime, bestMethod];
            console.log(row.join('\t\t'));
        }

        console.log('\n💡 Recommendations:');
        console.log('- Use database function method for best performance');
        console.log('- Fallback to direct aggregation if function fails');
        console.log('- Use batch processing only as last resort');
        console.log('- Consider refreshing materialized views regularly for optimal performance');
    }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new SessionStatsPerformanceTester();
    tester.run().catch(error => {
        console.error('Test failed:', error);
        process.exit(1);
    });
}

export default SessionStatsPerformanceTester;
