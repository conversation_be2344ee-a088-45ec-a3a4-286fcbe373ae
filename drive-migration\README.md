# 🚀 Drive-to-Lark Migrator

Ứng dụng di chuyển dữ liệu từ Google Drive sang Lark Drive với bảo toàn cấu trúc thư mục và quyền chia sẻ.

## 📋 Sprint 1 - Authentication & API Integration

### ✅ Hoàn thành
- ✅ Google Service Account Authentication với Domain-wide Delegation
- ✅ Lark Tenant Access Token với cache 2 giờ
- ✅ Test kết nối Google Drive API (files.list, files.get, permissions.list)
- ✅ Test kết nối Lark Drive API (upload file, tạo folder, gán quyền)
- ✅ Giao diện web đơn giản để test credentials

## 🛠️ Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd migration-to-lark/drive-migration
```

### 2. Cài đặt dependencies
```bash
npm install
```

### 3. Cấu hình môi trường
Sao chép file `.env.example` thành `.env` và điền thông tin:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:
```env
# Lark Configuration
LARK_APP_ID=your-lark-app-id
LARK_APP_SECRET=your-lark-app-secret
LARK_ACCESS_TOKEN=your-lark-access-token
LARK_REFRESH_TOKEN=your-lark-refresh-token

# Supabase Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
```

**Lưu ý**: 
- Bạn chỉ cần điền `LARK_APP_ID` và `LARK_APP_SECRET` thủ công
- `LARK_ACCESS_TOKEN` và `LARK_REFRESH_TOKEN` sẽ được script `get-lark-token` tự động cập nhật
- Google Service Account credentials được đọc từ file `google-service-account.json`, không cần cấu hình trong `.env`.

## 🔧 Thiết lập Google Service Account

### 1. Tạo Service Account
1. Vào [Google Cloud Console](https://console.cloud.google.com/)
2. Chọn project hoặc tạo project mới
3. Vào **IAM & Admin** > **Service Accounts**
4. Click **Create Service Account**
5. Điền tên và mô tả
6. Gán role **Project Editor** hoặc **Drive API** permissions

### 2. Tạo JSON Key
1. Click vào Service Account vừa tạo
2. Vào tab **Keys**
3. Click **Add Key** > **Create new key**
4. Chọn **JSON** và download file

### 3. Bật Domain-wide Delegation
1. Trong Service Account settings, check **Enable Google Workspace Domain-wide Delegation**
2. Lưu **Client ID** để dùng trong Admin Console

### 4. Cấu hình Admin Console
1. Vào [Google Admin Console](https://admin.google.com/)
2. **Security** > **API Controls** > **Domain-wide Delegation**
3. Click **Add new** và nhập:
   - **Client ID**: từ Service Account
   - **OAuth Scopes**:
     ```
     https://www.googleapis.com/auth/drive.readonly,
     https://www.googleapis.com/auth/drive.metadata.readonly,
     https://www.googleapis.com/auth/admin.directory.user.readonly
     ```

## 🦄 Thiết lập Lark App

### 1. Tạo Lark App
1. Vào [Lark Developer Console](https://open.larksuite.com/app)
2. Click **Create App**
3. Chọn **Custom App**
4. Điền thông tin app

### 2. Cấu hình Permissions
Trong app settings, bật các permissions:
- `contact:user:readonly`
- `drive:space:write`
- `drive:file:write`
- `drive:permission:write`

### 3. Lấy App Credentials
- **App ID**: Trong **Basic Information**
- **App Secret**: Trong **Basic Information**

### 4. Lấy User Access Token

#### Kiểm tra dependencies (Tùy chọn)
```bash
npm run check-deps
```

#### Lấy token tự động
```bash
npm run get-lark-token
```

Script sẽ tự động:
1. 📖 Đọc `LARK_APP_ID` và `LARK_APP_SECRET` từ file `.env`
2. 🔍 Kiểm tra và cài đặt `lark-user-token` nếu cần
3. 🌐 Tự động mở trình duyệt để đăng nhập Lark
4. 🎯 Lấy access token và refresh token
5. 💾 Tự động cập nhật vào file `.env`

**Lưu ý**: 
- Script sẽ luôn lấy token mới và ghi đè token cũ
- Access token có thời hạn sử dụng (thường là 2 giờ)
- Refresh token dùng để lấy access token mới khi hết hạn
- Ứng dụng sẽ tự động refresh token khi cần thiết

## 🧪 Kiểm tra kết nối

### 1. Chạy test script
```bash
npm run test
# hoặc
node src/test-apis.js
```

### 2. Sử dụng giao diện web
```bash
# Mở file frontend/public/index.html trong browser
# Hoặc serve bằng web server đơn giản:
npx serve frontend/public
```

Truy cập http://localhost:3000 và:
1. Paste Service Account JSON vào form
2. Nhập email người dùng test
3. Nhập Lark App ID và Secret
4. Click test để kiểm tra kết nối

## 📁 Cấu trúc dự án

```
drive-migration/
├── src/
│   ├── auth/
│   │   ├── google-auth.js      # Google Service Account auth
│   │   └── lark-auth.js        # Lark token management
│   ├── api/
│   │   ├── google-drive-api.js # Google Drive API wrapper
│   │   └── lark-drive-api.js   # Lark Drive API wrapper
│   └── test-apis.js            # Test script
├── scripts/
│   ├── get-lark-token.js       # Script lấy Lark token tự động
│   └── check-dependencies.js   # Kiểm tra dependencies
├── frontend/
│   └── public/
│       └── index.html          # Test UI
├── docs/
│   ├── migration-dir-with-permission.md
│   └── project-plan-agile.md
├── package.json
├── .env.example
└── README.md
```

## 🔍 Troubleshooting

### Google Drive API Errors
- **403 Forbidden**: Kiểm tra Domain-wide Delegation đã được cấu hình đúng
- **401 Unauthorized**: Kiểm tra Service Account JSON và private key
- **404 Not Found**: Kiểm tra user email có tồn tại trong domain

### Lark API Errors
- **Invalid App ID/Secret**: Kiểm tra credentials trong Lark Developer Console
- **Permission Denied**: Kiểm tra app permissions đã được bật
- **Token Expired**: Token sẽ tự động refresh, kiểm tra network connection

## 📝 Logs

Ứng dụng sẽ hiển thị logs chi tiết trong console:
- 🔑 Token operations
- 📁 API calls
- ✅ Success operations
- ❌ Error details

## 📤 Hướng dẫn Upload File lên Lark Drive

### Tổng quan
Sau khi đã thiết lập xong Lark App và lấy được access token, bạn có thể sử dụng chức năng upload file lên Lark Drive thông qua giao diện web hoặc API.

### 1. Chạy ứng dụng web

#### Khởi động backend server
```bash
npm run dev
# hoặc
node src/server.js
```

Server sẽ chạy tại `http://localhost:3001`

#### Khởi động frontend (terminal mới)
```bash
cd frontend
npm install  # chỉ cần chạy lần đầu
npm start
```

Frontend sẽ chạy tại `http://localhost:3000`

### 2. Sử dụng giao diện Migration

#### Bước 1: Truy cập Migration Page
1. Mở trình duyệt và vào `http://localhost:3000`
2. Click vào tab **"Migration"** trong navigation menu

#### Bước 2: Quét Google Drive
1. **Chọn phạm vi quét**:
   - Nhập email người dùng Google Drive
   - Chọn thư mục gốc hoặc thư mục cụ thể
   - Click **"Start Scanning"**

2. **Theo dõi tiến trình quét**:
   - Hệ thống sẽ quét toàn bộ files trong phạm vi đã chọn
   - Hiển thị progress bar và số lượng files đã quét

#### Bước 3: Chọn Files để Upload
1. **Xem danh sách files**:
   - Files được hiển thị dưới dạng tree structure
   - Có thể filter theo loại file, trạng thái download
   - Tìm kiếm files theo tên

2. **Chọn files**:
   - Click checkbox để chọn từng file
   - Sử dụng "Select All" để chọn tất cả
   - Có thể chọn theo thư mục

#### Bước 4: Bắt đầu Migration
1. Click **"Continue with X Selected Files"**
2. Điền thông tin migration:
   - **User Email**: Email người dùng
   - **Session ID**: ID của session quét
   - **Map Permissions**: Có áp dụng quyền truy cập không
   - **Target Root Folder**: Thư mục đích trên Lark (để trống = root)
   - **Preserve Folder Structure**: Giữ nguyên cấu trúc thư mục

3. Click **"Start Migration"**

#### Bước 5: Theo dõi tiến trình Upload
1. **Migration Dashboard** sẽ hiển thị:
   - Tổng quan tiến trình (files completed/total)
   - File đang được upload hiện tại
   - Danh sách files đã upload gần đây
   - Thống kê tốc độ upload

2. **Real-time updates**:
   - Progress bar cho từng file
   - Thông báo lỗi nếu có
   - Trạng thái hoàn thành

### 3. Sử dụng API trực tiếp

#### Upload single file
```bash
# Sử dụng curl để test API
curl -X POST http://localhost:3001/api/migration/start \
  -H "Content-Type: application/json" \
  -d '{
    "userEmail": "<EMAIL>",
    "sessionId": "scan-session-id",
    "options": {
      "mapPermissions": true,
      "preserveFolderStructure": true
    }
  }'
```

#### Kiểm tra tiến trình migration
```bash
curl http://localhost:3001/api/migration/status/MIGRATION_ID
```

### 4. Tính năng nâng cao

#### Smart Upload
- **Files nhỏ (< 20MB)**: Upload trực tiếp bằng API `upload_all`
- **Files lớn (≥ 20MB)**: Sử dụng chunked upload với các bước:
  1. `upload_prepare` - Chuẩn bị upload
  2. `upload_part` - Upload từng chunk (10MB/chunk)
  3. `upload_finish` - Hoàn tất upload

#### Tự động tạo thư mục
- Hệ thống tự động tạo cấu trúc thư mục trên Lark Drive
- Giữ nguyên hierarchy từ Google Drive
- Mapping folder permissions nếu được bật

#### Error Handling
- Retry tự động cho network errors
- Log chi tiết các lỗi upload
- Hiển thị files failed để retry

### 5. Troubleshooting Upload

#### Lỗi thường gặp
- **Token expired**: Chạy lại `npm run get-lark-token`
- **File too large**: Kiểm tra giới hạn file size (mặc định 2GB)
- **Permission denied**: Kiểm tra app permissions trong Lark Console
- **Network timeout**: Kiểm tra kết nối internet và retry

#### Kiểm tra logs
```bash
# Xem logs backend
tail -f logs/app.log

# Xem logs trong browser console (F12)
```

### 6. Cấu hình nâng cao

#### Tùy chỉnh upload settings
Chỉnh sửa file `.env`:
```env
# Upload configuration
UPLOAD_CHUNK_SIZE=10485760  # 10MB chunks
UPLOAD_MAX_FILE_SIZE=2147483648  # 2GB max
UPLOAD_CONCURRENT_CHUNKS=3  # Upload 3 chunks đồng thời
```

#### Cấu hình storage path
```env
# Local storage path for downloaded files
STORAGE_PATH=E:\  # Thay đổi đường dẫn lưu trữ
```

#### Performance và Timeout Configuration
```env
# Timeout settings (milliseconds)
QUERY_TIMEOUT=120000                # 2 phút cho queries thông thường
SESSION_STATS_TIMEOUT=120000        # 2 phút cho session stats calculation
LARGE_DATASET_TIMEOUT=300000        # 5 phút cho large dataset operations

# Performance settings
MAX_CONCURRENT_MIGRATIONS=10
CONNECTION_POOL_SIZE=20
```

#### Setup Performance Optimizations
```bash
# Chạy script để tạo database views và functions tối ưu performance
node scripts/setup-performance-optimizations.js

# Test performance improvements
node scripts/test-session-stats-performance.js
```

---

## 🚀 Tiếp theo

Sprint hiện tại đã hoàn thành:
- ✅ Google Drive scanning và file selection
- ✅ Lark Drive upload với smart chunking
- ✅ Real-time progress tracking
- ✅ Permission mapping
- ✅ Error handling và retry

Các tính năng đang phát triển:
- 🔄 Batch upload optimization
- 🔄 Resume interrupted uploads
- 🔄 Advanced permission mapping
- 🔄 Migration history và reporting

---
*Cập nhật: Migration system hoàn thành ngày 16-07-2025*
