-- Create optimized views for file statistics
-- This helps improve performance for session stats calculation

-- 1. Materialized view for user file statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_user_file_stats AS
SELECT 
    user_email,
    domain,
    COUNT(*) as total_files,
    SUM(size) as total_size,
    COUNT(*) FILTER (WHERE download_status = 'downloaded') as downloaded_files,
    SUM(size) FILTER (WHERE download_status = 'downloaded') as downloaded_size,
    COUNT(*) FILTER (WHERE download_status = 'failed') as failed_files,
    COUNT(*) FILTER (WHERE download_status IS NULL OR download_status = 'not_downloaded') as pending_files,
    SUM(size) FILTER (WHERE download_status IS NULL OR download_status = 'not_downloaded') as pending_size,
    -- MIME type breakdown
    COUNT(*) FILTER (WHERE mime_type LIKE 'image/%') as image_files,
    COUNT(*) FILTER (WHERE mime_type LIKE 'video/%') as video_files,
    COUNT(*) FILTER (WHERE mime_type LIKE 'application/%') as document_files,
    COUNT(*) FILTER (WHERE mime_type LIKE 'text/%') as text_files,
    -- Size categories
    COUNT(*) FILTER (WHERE size < 1048576) as small_files,      -- < 1MB
    COUNT(*) FILTER (WHERE size BETWEEN 1048576 AND 104857600) as medium_files, -- 1MB - 100MB
    COUNT(*) FILTER (WHERE size > 104857600) as large_files,    -- > 100MB
    -- Timestamps
    MIN(created_time) as earliest_file,
    MAX(created_time) as latest_file,
    NOW() as last_updated
FROM scanned_files_cuong
GROUP BY user_email, domain;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_user_file_stats_user_domain 
ON mv_user_file_stats(user_email, domain);

-- 2. View for quick session stats calculation
CREATE OR REPLACE VIEW v_session_stats_quick AS
SELECT 
    user_email,
    domain,
    mime_type,
    COUNT(*) as file_count,
    SUM(size) as total_size,
    COUNT(*) FILTER (WHERE download_status IS NULL OR download_status = 'not_downloaded') as downloadable_count,
    SUM(size) FILTER (WHERE download_status IS NULL OR download_status = 'not_downloaded') as downloadable_size
FROM scanned_files_cuong
GROUP BY user_email, domain, mime_type;

-- 3. Function to get session stats efficiently
CREATE OR REPLACE FUNCTION get_session_stats(
    selected_users TEXT[],
    skip_mime_types TEXT[] DEFAULT '{}'
) RETURNS TABLE (
    total_files BIGINT,
    total_size BIGINT,
    downloadable_files BIGINT,
    downloadable_size BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(file_count)::BIGINT as total_files,
        SUM(total_size)::BIGINT as total_size,
        SUM(downloadable_count)::BIGINT as downloadable_files,
        SUM(downloadable_size)::BIGINT as downloadable_size
    FROM v_session_stats_quick
    WHERE user_email = ANY(selected_users)
      AND (array_length(skip_mime_types, 1) IS NULL OR mime_type != ALL(skip_mime_types));
END;
$$ LANGUAGE plpgsql;

-- 4. Function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_user_file_stats()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_file_stats;
    
    -- Log refresh
    INSERT INTO system_logs (level, message, created_at)
    VALUES ('info', 'Refreshed mv_user_file_stats materialized view', NOW());
    
EXCEPTION WHEN OTHERS THEN
    -- Log error if refresh fails
    INSERT INTO system_logs (level, message, error_details, created_at)
    VALUES ('error', 'Failed to refresh mv_user_file_stats', SQLERRM, NOW());
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- 5. Create system_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGSERIAL PRIMARY KEY,
    level TEXT NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error')),
    message TEXT NOT NULL,
    error_details TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for system_logs
CREATE INDEX IF NOT EXISTS idx_system_logs_level_created_at 
ON system_logs(level, created_at DESC);

-- 6. Schedule automatic refresh of materialized view (requires pg_cron extension)
-- This is optional and requires pg_cron to be enabled
-- SELECT cron.schedule('refresh-user-stats', '0 */6 * * *', 'SELECT refresh_user_file_stats();');

-- 7. Create indexes for better performance on scanned_files_cuong if not exists
CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_user_download_mime 
ON scanned_files_cuong(user_email, download_status, mime_type);

CREATE INDEX IF NOT EXISTS idx_scanned_files_cuong_size_category 
ON scanned_files_cuong(
    CASE 
        WHEN size < 1048576 THEN 'small'
        WHEN size BETWEEN 1048576 AND 104857600 THEN 'medium'
        ELSE 'large'
    END
);

-- 8. Grant permissions
GRANT SELECT ON mv_user_file_stats TO PUBLIC;
GRANT SELECT ON v_session_stats_quick TO PUBLIC;
GRANT EXECUTE ON FUNCTION get_session_stats(TEXT[], TEXT[]) TO PUBLIC;
GRANT EXECUTE ON FUNCTION refresh_user_file_stats() TO PUBLIC;

-- Initial refresh of materialized view
SELECT refresh_user_file_stats();

-- Add comment
COMMENT ON MATERIALIZED VIEW mv_user_file_stats IS 'Materialized view for user file statistics - refreshed every 6 hours';
COMMENT ON VIEW v_session_stats_quick IS 'Quick view for session statistics calculation';
COMMENT ON FUNCTION get_session_stats(TEXT[], TEXT[]) IS 'Function to efficiently calculate session statistics for selected users';
